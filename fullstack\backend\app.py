import os
import sys
import json
import random
import sqlite3
from datetime import datetime
from itertools import combinations
from flask import Flask, request, jsonify, render_template, send_from_directory

from modules.ppObjects import Pick, BoostPromo, ProtectedPromo
from modules.RROptimizer import analyze_all_splits
from modules.HandiCapperAccuracyModel import main_model
from data_science_modules.planet_scale_port import get_todays_events_with_handicappers, submit_event, generate_event_id, generate_admin_event_id, get_connection

app = Flask(__name__, static_folder=os.path.join(os.path.dirname(__file__), "../frontend/dist"), static_url_path="/")

# Global object lists
pick_objects = []
boost_promo_objects = []
protected_promo_objects = []
next_id = 1
user_accuracy = 0.0

_WORKSPACE_ROOT = os.path.dirname(os.path.abspath(__file__))
if _WORKSPACE_ROOT not in sys.path:
    sys.path.append(_WORKSPACE_ROOT)

_PROJECT_ROOT = os.path.dirname(_WORKSPACE_ROOT)
if _PROJECT_ROOT not in sys.path:
    sys.path.append(_PROJECT_ROOT)

DB_PATH = os.path.join(_WORKSPACE_ROOT, "data", "pick_confidence.db")
_DB_DIR = os.path.dirname(DB_PATH)
if not os.path.exists(_DB_DIR):
    os.makedirs(_DB_DIR)
    print(f"Created data directory: {_DB_DIR}")


origin_profiles = {
    "ChalkBoardPI": 0.80,
    "HarryLock": 0.71,
    "DanGamblePOD": 0.78,
    "DanGambleAIEdge": 90.0,
    "GameScript": 0.80,
    "Winible": 0.83,
    "DoberMan": 0.76,
    "JoshMiller": 0.60,
    "Me": lambda: user_accuracy
}

def confidence_score(decimal_odds, expert_confidence, expert_accuracy):
    implied_prob = 1 / decimal_odds
    c = expert_confidence / 100
    a = expert_accuracy / 100
    score = 100 * (a * c + (1 - a) * implied_prob)
    return score

def generate_round_robin_subparlays(pick_list, subgroup_size):
    """Returns a list of subparlays (combinations) of picks."""
    return [list(combo) for combo in combinations(pick_list, subgroup_size)]

def get_all_objects():
    return pick_objects, boost_promo_objects, protected_promo_objects

@app.route("/api/get_picks", methods=["GET"])
def get_picks():
    return jsonify({
    "objects": [p.to_dict() for p in pick_objects]
    })

@app.route('/', defaults={'path': ''})
@app.route('/<path:path>')
def ServeReactApp(path):
    FilePath = os.path.join(app.static_folder, path)
    if path != "" and os.path.exists(FilePath):
        return send_from_directory(app.static_folder, path)
    return send_from_directory(app.static_folder, 'index.html')

# Ensure that API routes are defined BEFORE this point, 
# or that this catch-all is specific enough not to interfere with static assets if static_url_path is also '/'.
# If static_url_path="/", Flask automatically tries to serve static files first.
# If /favicon.ico or /assets/index-*.js is requested, Flask's static file handler should serve it.

@app.route("/api/optimize_split", methods=["GET"])
def optimize_split():
    sorted_picks = sorted(pick_objects, key=lambda x: getattr(x, "confidence_score", 0), reverse=True)
    print(f"Optimizer: Processing {len(pick_objects)} picks. Sorted: {[p.name for p in sorted_picks]}")

    if len(sorted_picks) < 2:
        print("Optimizer: Not enough picks to run analysis (need at least 2).")
        return jsonify({
            "best_score": 0.0,
            "best_config": "Not enough picks to optimize (Need at least 2).",
            "sorted_picks": [p.to_dict() for p in sorted_picks],
            "subparlays": []
        }), 200 # Return 200 so frontend can parse the message

    best_score, best_label = analyze_all_splits(sorted_picks)
    # total_capital = request.json.get("total_capital", 0)

    picks_to_use = []
    subgroup_size = 2  # Default

    # Ensure best_label is a string before string operations
    if isinstance(best_label, str):
        if "Full List" in best_label:
            picks_to_use = sorted_picks
        elif "Left" in best_label:
            try:
                split_index_str = best_label.split("index")[1].strip()
                split_index = int(split_index_str)
                picks_to_use = sorted_picks[:split_index]
            except (IndexError, ValueError) as e:
                print(f"Optimizer: Error parsing split_index from left-sided '{best_label}': {e}. Defaulting picks_to_use.")
                picks_to_use = [] 
        elif "Right" in best_label:
            try:
                split_index_str = best_label.split("index")[1].strip()
                split_index = int(split_index_str)
                picks_to_use = sorted_picks[split_index:]
            except (IndexError, ValueError) as e:
                print(f"Optimizer: Error parsing split_index from right-sided '{best_label}': {e}. Defaulting picks_to_use.")
                picks_to_use = []
        else:
            print(f"Optimizer: best_label '{best_label}' did not match expected patterns. Defaulting picks_to_use to empty.")
            picks_to_use = []

        import re
        match = re.search(r"Size (\d+)", best_label)
        if match:
            subgroup_size = int(match.group(1))
        else:
            print(f"Optimizer: Could not extract 'Size X' from best_label '{best_label}'. Defaulting subgroup_size to {subgroup_size}.")
    else:
        # This case should ideally not be hit if len(sorted_picks) >= 2 because analyze_all_splits should return a string label.
        # However, as a fallback if best_label is unexpectedly not a string (e.g. None, though covered by initial check).
        print(f"Optimizer: best_label was not a string ('{best_label}'). Defaulting picks_to_use and subgroup_size.")
        picks_to_use = []
        subgroup_size = 2
        best_label = "Error: Invalid optimization label received."
        best_score = 0.0

    subparlays = generate_round_robin_subparlays(picks_to_use, subgroup_size)
    final_best_score = best_score if isinstance(best_score, (int, float)) else 0.0

    return jsonify({
        "best_score": final_best_score,
        "best_config": best_label,
        "sorted_picks": [p.to_dict() for p in sorted_picks],
        "subparlays": [[p.to_dict() for p in sub] for sub in subparlays]
    })



@app.route("/api/process", methods=["POST"])
def process():
    global next_id
    data = request.get_json()

    try:
        print("🟢 PAYLOAD RECEIVED:", data)

        name = data.get("name", "").strip()
        pick_origins = data.get("pick_origin", [])  # [{ name, confidence }]
        print("odds:" + str(data.get("odds", 0)))
        odds = float(data.get("odds", 0))
        leagues = data.get("league", [])
        reusable = data.get("reusable", True)
        capital_limit = int(data.get("capital_limit", 0))
        mutual_exclusion = int(data.get("mutual_exclusion", -1))
        pick_type = data.get("pick_type", "MoneyLine")
        player_team = data.get("player_team", "None")
        stat_type = data.get("stat_type", "MoneyLine")

        if not name or not odds or not pick_origins or not leagues:
            return jsonify({"response": "Missing required fields", "success": False}), 400

        implied_prob = round(1 / odds, 4)  # crowd probability from odds
        today = datetime.today().strftime("%Y-%m-%d")

        expert_predictions = []
        total_score = 0

        for origin_obj in pick_origins:
            origin = origin_obj.get("name")
            print(origin_obj.get("confidence"))
            origin_conf = origin_obj.get("confidence")

            if not origin:
                continue  # Skip invalid entries

            # 💡 Ensure origin_conf is a usable float
            try:
                used_conf = float(origin_conf)
            except Exception as e:
                used_conf = 75.0  # fallback if None or not a number

            # 🔧 Normalize the origin key by removing spaces
            origin_key = origin.replace(" ", "")

            if origin_key not in origin_profiles:
                raise KeyError(f"Origin key '{origin_key}' not found in origin_profiles")

            origin_accuracy = origin_profiles[origin_key]() if callable(origin_profiles[origin_key]) else origin_profiles[origin_key]


            norm_conf = used_conf / 100.0
            # Extract shared prediction direction
            prediction = int(data.get("prediction", 1))  # 1 = Higher, 0 = Lower

            for origin_obj in pick_origins:
                origin = origin_obj.get("name")
                origin_conf = origin_obj.get("confidence")

                if not origin:
                    continue  # Skip invalid entries

                # 💡 Ensure origin_conf is a usable float
                try:
                    used_conf = float(origin_conf)
                except Exception:
                    used_conf = 75.0  # fallback if None or not a number

                # 🧠 Historical accuracy
                origin_accuracy = origin_profiles[origin]() if callable(origin_profiles[origin]) else origin_profiles[origin]

                norm_conf = used_conf / 100.0

                # ⬅️ Use the shared prediction value for all experts
                expert_predictions.append((origin, prediction, norm_conf))

                # 🧮 Confidence score calculation
                score = confidence_score(odds, used_conf, origin_accuracy)
                total_score += score

        # final_score = round(total_score / len(expert_predictions), 2) if expert_predictions else 0
        # print(final_score)

        if pick_type == "MoneyLine":
            team_a = name
            team_b = "Other"
            player_team = "None"
        else:
            team_a = "Over"
            team_b = "Under"

        print("🧠 Parsed expert predictions:", expert_predictions)

        status_messages = []
        success_count = 0

        for league in leagues:
            event_id = generate_event_id(name, league)
            # Check if the event already exists
            conn = sqlite3.connect(DB_PATH)
            cursor = conn.cursor()
            cursor.execute("SELECT 1 FROM events WHERE event_id = ?", (event_id,))
            exists = cursor.fetchone() is not None
            conn.close()

            if not exists:
                # Insert event & predictions as usual
                success, message = submit_event(
                    event_id=event_id,
                    event_date=today,
                    league=league,
                    team_a=team_a,
                    team_b=team_b,
                    crowd_probability=implied_prob,
                    expert_predictions=expert_predictions,
                    actual_result=None,
                    pick_type=pick_type,
                    player_team=player_team,
                    stat_type=stat_type
                )
            else:
                # Just insert additional expert predictions and update crowd prob
                try:
                    conn = sqlite3.connect(DB_PATH)
                    cursor = conn.cursor()

                    # ✅ Update crowd_probability (optional)
                    cursor.execute("""
                        INSERT OR REPLACE INTO crowd_predictions (event_id, crowd_probability)
                        VALUES (?, ?)
                    """, (event_id, implied_prob))

                    # ✅ Append new expert predictions
                    for expert_name, prediction, confidence in expert_predictions:
                        cursor.execute("""
                            INSERT INTO expert_predictions (event_id, expert_name, prediction, confidence)
                            VALUES (?, ?, ?, ?)
                        """, (event_id, expert_name, prediction, confidence))

                        # Ensure expert exists in reliability table
                        cursor.execute("SELECT 1 FROM expert_reliability WHERE expert_name = ?", (expert_name,))
                        if not cursor.fetchone():
                            cursor.execute("""
                                INSERT INTO expert_reliability (expert_name)
                                VALUES (?)
                            """, (expert_name,))

                    conn.commit()
                    conn.close()
                    success = True
                    message = "Existing event updated with new expert predictions."

                except Exception as e:
                    success = False
                    message = f"Error updating existing event: {e}"

            status_messages.append(f"[{league}] {message}")

            #----USE ML MODEL TO ANALYZE PICK-------
            prediction_result = main_model(event_id)
            ml_prob = prediction_result["combined_prob"]
            logistic_prob = prediction_result["logistic_prob"]
            bayesian_prob = prediction_result["bayesian_prob"]
            bayesian_conf = prediction_result["quality_score"]


            # Calculate implied probability
            implied_prob = round(1 / odds, 4)
            print("IP:" + str(implied_prob))

            # Step 1: Compute raw difference
            raw_score = ml_prob - implied_prob  # can range from -1 to +1

            print("RS:"+ str(raw_score))

            # Step 2: Clamp to ±0.2 range
            clamped_score = max(min(raw_score, 0.2), -0.2)

            print("CS:"+ str(clamped_score))

            # Step 3: Scale to 0–100
            scaled_score = round((clamped_score + 0.2) / 0.4 * 100)

            print("FS:"+ str(scaled_score))

            final_score = scaled_score  # this is now an int from 0 to 100

            print("odds:" + str(odds))

            if success:
                new_pick = Pick(
                    name=name,
                    odds=odds,
                    confidence=final_score,
                    mutual_exclusion_group=mutual_exclusion,
                    league=league,
                    event_id=event_id,
                    bayesian_prob =bayesian_prob, 
                    logistic_prob=logistic_prob, 
                    bayesian_conf=bayesian_conf,
                    stat_type=stat_type,
                    reusable=reusable,
                    capital_limit=capital_limit
                )
                pick_objects.append(new_pick)
                next_id += 1
                success_count += 1

        return jsonify({
            "response": " | ".join(status_messages),
            "objects": [p.__dict__ for p in pick_objects],
            "success": success_count == len(leagues)
        })

    except Exception as e:
        print("❌ SERVER ERROR:", e)
        return jsonify({"response": f"Server error: {str(e)}", "success": False}), 500



@app.route("/api/create_boost_promo", methods=["POST"])
def create_boost_promo():
    data = request.get_json()
    boost_percentage = int(data.get("boost_percentage", 0))
    required_picks = int(data.get("required_picks", 0))
    same_sport = data.get("same_sport", False)

    boost = BoostPromo(boost_percentage, required_picks, same_sport)
    boost_promo_objects.append(boost.__dict__)

    return jsonify({"response": f"Created Boost Promo: {boost.name}", "boost_promos": boost_promo_objects})

@app.route("/api/create_protected_promo", methods=["POST"])
def create_protected_promo():
    data = request.get_json()
    protected_amount = int(data.get("protected_amount", 0))
    eligible_leagues = data.get("eligible_leagues", [])

    protected = ProtectedPromo(protected_amount, eligible_leagues)
    protected_promo_objects.append(protected.__dict__)

    return jsonify({"response": f"Created Protected Play Promo: {protected.name}", "protected_promos": protected_promo_objects})

@app.route("/api/edit", methods=["POST"])
def edit():
    global pick_objects
    data = request.get_json()
    obj_id = data.get("id")

    for obj in pick_objects:
        if obj.pID == obj_id:
            # Update fields on the Pick object
            obj.name = data.get("name", obj.name)
            obj.decimalOdds = float(data.get("odds", obj.decimalOdds))
            obj.pick_origin = data.get("pick_origin", obj.pick_origin)
            obj.league = data.get("league", obj.league)
            obj.reusable = data.get("reusable", obj.reusable)
            obj.capital_limit = int(data.get("capital_limit", obj.capital_limit))
            obj.gameID = int(data.get("mutual_exclusion", obj.gameID))
            obj.pick_type = data.get("pick_type", obj.pick_type)
            obj.player_team = data.get("player_team", obj.player_team)
            obj.stat_type = data.get("stat_type", obj.stat_type)

            name = obj.name
            odds = obj.decimalOdds
            leagues = obj.league
            pick_origins = obj.pick_origin
            pick_type = obj.pick_type
            player_team = obj.player_team
            stat_type = obj.stat_type

            # Determine team_a and team_b based on pick_type
            if pick_type == "MoneyLine":
                team_a = name
                team_b = "Other"
                player_team = "None"
            else:
                team_a = "Over"
                team_b = "Under"

            implied_prob = round(1 / odds, 4)
            today = datetime.today().strftime("%Y-%m-%d")

            # Recalculate expert prediction score
            expert_predictions = []
            total_score = 0

            for origin_obj in pick_origins:
                origin = origin_obj.get("name")
                origin_conf = origin_obj.get("confidence", None)
                prediction = origin_obj.get("prediction", 1)  # default to Higher

                origin_accuracy = origin_profiles[origin]() if callable(origin_profiles[origin]) else origin_profiles[origin]
                norm_conf = origin_conf / 100 if origin_conf is not None else None

                expert_predictions.append((origin, prediction, norm_conf))

                used_conf = origin_conf if origin_conf is not None else 75.0
                score = confidence_score(odds, used_conf, origin_accuracy)
                total_score += score

            final_score = round(total_score / len(expert_predictions), 2) if expert_predictions else 0
            obj.confidence = None
            obj.confidence_score = final_score

            # Update the database
            for league in leagues:
                event_id = generate_event_id(name, league)
                obj.event_id = event_id

                try:
                    conn = sqlite3.connect(DB_PATH)
                    cursor = conn.cursor()

                    cursor.execute("""
                        INSERT OR REPLACE INTO events (
                            event_id, event_date, league,
                            team_a, team_b, actual_result,
                            pick_type, player_team, stat_type
                        ) VALUES (?, ?, ?, ?, ?, COALESCE(
                            (SELECT actual_result FROM events WHERE event_id = ?), NULL
                        ), ?, ?, ?)
                    """, (
                        event_id, today, league,
                        team_a, team_b, event_id,
                        pick_type, player_team, stat_type
                    ))

                    cursor.execute("""
                        INSERT OR REPLACE INTO crowd_predictions (event_id, crowd_probability)
                        VALUES (?, ?)
                    """, (event_id, implied_prob))

                    cursor.execute("DELETE FROM expert_predictions WHERE event_id = ?", (event_id,))
                    for origin, prediction, confidence in expert_predictions:
                        cursor.execute("""
                            INSERT INTO expert_predictions (event_id, expert_name, prediction, confidence)
                            VALUES (?, ?, ?, ?)
                        """, (event_id, origin, prediction, confidence))

                        cursor.execute("SELECT 1 FROM expert_reliability WHERE expert_name = ?", (origin,))
                        if not cursor.fetchone():
                            cursor.execute("""
                                INSERT INTO expert_reliability (expert_name)
                                VALUES (?)
                            """, (origin,))

                    conn.commit()
                    conn.close()

                except Exception as e:
                    print(f"❌ DB Error while editing {event_id}: {e}")

            break

    return jsonify({"objects": [p.__dict__ for p in pick_objects]})

##### CURRENT SPOT IS CREATING RELIABLE EVENT ID FROM PICK ID ####
def get_event_id_from_pick_id(pick_id):
    for pick in pick_objects:
        print(pick.pID)
        if pick.pID == pick_id:
            return pick.event_id
    return None



@app.route("/api/submit_verified", methods=["POST"])
def submit_verified():
    data = request.get_json()
    verified = data.get("verified", [])

    print(pick_objects)

    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    updated_ids = []
    localID = 0

    for item in verified:
        localID+=1
        pick_id = localID
        

        # Assume item["actual_result"] is 1 if user marked it "Verified"
        user_verification = item["actual_result"]

        # Find pick object by ID
        pick = next((p for p in pick_objects if hasattr(p, "pID") and p.pID == pick_id), None)
        if not pick:
            print(f"❌ Pick ID {pick_id} not found in memory.")
            continue

        event_id = getattr(pick, "event_id", None)
        if not event_id:
            print(f"❌ Pick ID {pick_id} has no event_id.")
            continue

        # 🔍 Interpret prediction direction:
        #  - If expert said "Higher" and user marked as 1 → event happened ✅
        #  - If expert said "Lower" and user marked as 1 → event did NOT happen ❌
        # We'll default to the first expert's prediction signal (they're all assumed to match)
        cursor.execute("SELECT prediction FROM expert_predictions WHERE event_id = ? LIMIT 1", (event_id,))
        row = cursor.fetchone()

        if row:
            expert_prediction = row[0]  # 1 = Higher, 0 = Lower

            if expert_prediction == 1:  # Higher = event is expected to occur
                actual_result = 1 if user_verification == 1 else 0
            else:  # Lower = expert expects event NOT to occur
                actual_result = 0 if user_verification == 1 else 1
        else:
            print(f"⚠️ No expert prediction found for {event_id}, assuming default.")
            actual_result = user_verification
        event_id = get_event_id_from_pick_id(pick_id)
        print(event_id)

        # ✅ Find pick object by ID (from actual class instances)
        pick = next((p for p in pick_objects if hasattr(p, "pID") and p.pID == pick_id), None)
        if not pick:
            print(f"❌ Pick ID {pick_id} not found in memory.")
            continue

        event_id = getattr(pick, "event_id", None)
        if not event_id:
            print(f"❌ Pick ID {pick_id} has no event_id.")
            continue

        print(f"✅ Updating event_id: {event_id} → actual_result: {actual_result}")
        try:
            cursor.execute("""
                UPDATE events
                SET actual_result = ?
                WHERE event_id = ?
            """, (actual_result, event_id))

            if cursor.rowcount > 0:
                updated_ids.append(event_id)
            else:
                print(f"⚠️ No rows updated for {event_id} (may not exist in DB).")

        except Exception as e:
            print(f"❌ DB error updating {event_id}: {e}")

    conn.commit()
    conn.close()

    return jsonify({"message": f"Updated {len(updated_ids)} events with actual results."})






@app.route("/api/load_sample_picks", methods=["POST"])
def load_sample_picks():
    global pick_objects
    pick_objects = []

    num_picks = 8
    example_names = ["Lakers ML", "Yankees -1.5", "Chiefs +3", "Over 8.5", "Under 220", "Dodgers ML", "Ravens -2.5", "Heat +6", "Bills ML", "Nets Over 230"]
    leagues = ["NBA", "NFL", "MLB", "NHL"]

    for i in range(num_picks):
        name = random.choice(example_names) + f" #{i+1}"
        odds = round(random.uniform(1.05, 2.5), 2)
        mutual_exclusion_group = random.randint(0, 5)
        league = random.choice(leagues)
        reusable = random.choice([True, False])
        capital_limit = random.randint(10, 100)
        stat_type = "MoneyLine"
        event_id = f"SAMPLE-{i+1}"

        # Generate synthetic model probabilities
        bayesian_prob = round(random.uniform(0.4, 0.9), 2)
        logistic_prob = round(random.uniform(0.4, 0.9), 2)
        bayesian_conf = round(random.uniform(0.5, 0.9), 2)

        # Calculate final confidence score using model-weighted blend
        combined_prob = round(
            bayesian_conf * bayesian_prob + (1 - bayesian_conf) * logistic_prob, 4
        )
        implied_prob = 1 / odds
        raw_score = combined_prob - implied_prob
        clamped = max(min(raw_score, 0.2), -0.2)
        scaled_score = round((clamped + 0.2) / 0.4 * 100, 2)

        # Create Pick object
        new_pick = Pick(
            name=name,
            odds=odds,
            confidence=scaled_score,
            mutual_exclusion_group=mutual_exclusion_group,
            league=league,
            event_id=event_id,
            bayesian_prob=bayesian_prob,
            logistic_prob=logistic_prob,
            bayesian_conf=bayesian_conf,
            stat_type=stat_type,
            reusable=reusable,
            capital_limit=capital_limit
        )

        pick_objects.append(new_pick)

    return jsonify({
        "message": f"{num_picks} sample picks loaded.",
        "objects": [p.to_dict() for p in pick_objects]
    })
    
@app.route("/api/load_user_picks", methods=["POST"])
def load_user_picks():
    """
    Load user picks from frontend PicksContext into backend pick_objects for optimizer functionality.
    Expects JSON payload with 'picks' array containing user pick data.
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "message": "No data provided.",
                "objects": []
            }), 400

        user_picks = data.get("picks", [])

        global pick_objects
        pick_objects = []  # Clear existing picks
        Pick.pID_counter = 0  # Reset ID counter

        # Convert user picks to backend Pick objects
        for pick_data in user_picks:
            try:
                # Map PicksContext format to backend Pick format
                new_pick = Pick(
                    name=pick_data.get("playerName", "Unknown Player"),
                    odds=1.5,  # Default odds - could be enhanced to get from pick data
                    confidence=pick_data.get("confidence", 75),
                    mutual_exclusion_group=-1,  # Default group
                    league=["Unknown"],  # Default league as list
                    event_id=pick_data.get("id", f"user_pick_{len(pick_objects)}"),
                    bayesian_prob=0.5,  # Default probability
                    logistic_prob=0.5,  # Default probability
                    bayesian_conf=0.5,  # Default confidence
                    stat_type=pick_data.get("betType", "Unknown"),
                    reusable=True,
                    capital_limit=0
                )
                pick_objects.append(new_pick)
            except Exception as e:
                print(f"Error processing pick {pick_data}: {e}")
                continue

        return jsonify({
            "message": f"Loaded {len(pick_objects)} user picks successfully.",
            "objects": [p.to_dict() for p in pick_objects]
        })

    except Exception as e:
        print(f"Error in load_user_picks: {e}")
        return jsonify({
            "message": f"Error loading user picks: {str(e)}",
            "objects": []
        }), 500


@app.route("/api/clear_picks", methods=["POST"])
def clear_picks():
    global pick_objects
    pick_objects = []
    Pick.pID_counter = 0  # reset ID counter

    # Optional: clear optimizer results too if you're storing those separately
    # e.g., if you eventually save best_score or best_label in global vars

    return jsonify({
        "message": "All picks cleared.",
        "objects": pick_objects
    })


@app.route("/api/delete", methods=["POST"])
def delete():
    data = request.get_json()
    obj_id = data.get("id")

    global pick_objects
    deleted_pick = None

    # Find the pick
    for obj in pick_objects:
        if obj["id"] == obj_id:
            deleted_pick = obj
            break

    if deleted_pick:
        name = deleted_pick["name"]
        leagues = deleted_pick["league"]
        for league in leagues:
            event_id = generate_event_id(name, league)
            try:
                conn = sqlite3.connect(DB_PATH)
                cursor = conn.cursor()
                cursor.execute("DELETE FROM expert_predictions WHERE event_id = ?", (event_id,))
                cursor.execute("DELETE FROM crowd_predictions WHERE event_id = ?", (event_id,))
                cursor.execute("DELETE FROM events WHERE event_id = ?", (event_id,))
                conn.commit()
                conn.close()
            except Exception as e:
                print(f"DB error while deleting event {event_id}: {e}")

        # Remove from in-memory list
        pick_objects = [obj for obj in pick_objects if obj["id"] != obj_id]

    return jsonify({
   "objects": [p.to_dict() for p in pick_objects]
    })


@app.route("/api/update_accuracy", methods=["POST"])
def update_accuracy():
    global user_accuracy
    data = request.get_json()
    try:
        user_accuracy = float(data.get("accuracy", 0.0))
    except ValueError:
        user_accuracy = 0.0
    return jsonify({"message": f"Accuracy updated to {user_accuracy}", "user_accuracy": user_accuracy})

@app.route("/api/handicappers", methods=["GET"])
def get_handicappers():
    """
    Placeholder endpoint to prevent 404 errors.
    Frontend handles handicapper data through local caching.
    """
    return jsonify({
        "success": True,
        "message": "Handicappers endpoint placeholder",
        "handicappers": []
    })

@app.route("/api/handicappers/<int:handicapper_id>", methods=["GET"])
def get_handicapper_profile(handicapper_id):
    """
    Placeholder endpoint to prevent 404 errors.
    Frontend handles handicapper profiles through local caching.
    """
    return jsonify({
        "success": True,
        "message": "Handicapper profile endpoint placeholder",
        "handicapper": {
            "id": handicapper_id,
            "name": f"Handicapper {handicapper_id}",
            "accuracy": "N/A",
            "sports": "Multiple Sports",
            "picks": []
        }
    })

@app.route("/api/favorites", methods=["GET"])
def get_favorites():
    """
    Placeholder endpoint to prevent 404 errors.
    Frontend handles favorites through local storage.
    """
    return jsonify({
        "success": True,
        "message": "Favorites endpoint placeholder",
        "favorites": []
    })

@app.route("/api/favorites", methods=["POST"])
def save_favorites():
    """
    Placeholder endpoint to prevent 404 errors.
    Frontend handles favorites through local storage.
    """
    return jsonify({
        "success": True,
        "message": "Favorites saved (placeholder)",
        "favorites": []
    })

@app.route("/api/todays_events", methods=["GET"])
@app.route("/todays_events", methods=["GET"])
def get_todays_events():
    """
    API endpoint to retrieve events with handicapper predictions for a specific date.
    
    Query Parameters:
        date (optional): Date in YYYY-MM-DD format. If not provided, uses today's date.
    
    Returns:
        JSON response containing a list of events with their associated
        handicapper predictions, or an error message if something goes wrong.
    """
    try:
        # Get date parameter from query string, default to today if not provided
        custom_date = request.args.get('date')
        if custom_date:
            # Validate date format
            try:
                datetime.strptime(custom_date, "%Y-%m-%d")
                target_date = custom_date
            except ValueError:
                return jsonify({
                    "success": False,
                    "message": "Invalid date format. Please use YYYY-MM-DD format.",
                    "events": []
                }), 400
        else:
            target_date = datetime.today().strftime("%Y-%m-%d")
        
        events = get_todays_events_with_handicappers(target_date)
        
        events_data = []
        for event in events:
            events_data.append(event.to_dict())
        
        return jsonify({
            "success": True,
            "message": f"Retrieved {len(events_data)} events for {target_date}",
            "events": events_data,
            "date": target_date
        })
        
    except Exception as e:
        print(f"❌ Error in /api/todays_events endpoint: {e}")
        return jsonify({
            "success": False,
            "message": f"Error retrieving events: {str(e)}",
            "events": []
        }), 500



@app.route("/api/add_event", methods=["POST"])
def add_event():
    """Insert a single event (or one per league) into the events table. Minimal logic, password-protected."""
    data = request.get_json()

    if data.get("admin_password") != "ppadmin42":
        return jsonify({"success": False, "message": "Unauthorized"}), 401

    name = data.get("name", "").strip()
    leagues = data.get("league", [])
    event_date = data.get("eventDate") or data.get("event_date") or datetime.today().strftime("%Y-%m-%d")
    pick_type = data.get("pick_type", "MoneyLine")
    player_team = data.get("player_team", "None")
    stat_type = data.get("stat_type", "MoneyLine")
    player_name = data.get("player_name")
    stat_threshold = data.get("stat_threshold")
    team_a = data.get("team_a", name)  # Default to name if not provided
    team_b = data.get("team_b", "Other")  # Default to "Other" if not provided
    pick_origin = data.get('pick_origin', [])

    if not name or not leagues:
        return jsonify({"success": False, "message": "Missing required fields"}), 400

    # Import PlanetScale connection function
    try:
        from data_science_modules.planet_scale_port import get_connection
    except ImportError:
        return jsonify({"success": False, "message": "Database connection module not available"}), 500

    inserted = 0

    try:
        with get_connection() as conn:
            cursor = conn.cursor()
            event_id = None  # Ensure event_id is defined outside the loop
            expert_predictions_inserted = 0

            # Test database connection
            cursor.execute("SELECT 1")
            test_result = cursor.fetchone()
            print(f"🔗 Database connection test: {test_result}")

            # Check if tables exist
            cursor.execute("SHOW TABLES LIKE 'events'")
            events_table = cursor.fetchone()
            cursor.execute("SHOW TABLES LIKE 'expert_predictions'")
            predictions_table = cursor.fetchone()
            print(f"📋 Tables exist - events: {bool(events_table)}, expert_predictions: {bool(predictions_table)}")

            print(f"🔄 Processing {len(leagues)} leagues: {leagues}")
            print(f"🔄 Expert predictions to insert: {len(pick_origin) if pick_origin else 0}")

            for league in leagues:
                event_id = generate_admin_event_id(
                    event_date, league, pick_type, team_a, team_b,
                    player_name, stat_threshold, stat_type
                )
                print(f"🔄 Generated event_id for {league}: {event_id}")
                print(f"🔄 Event data: date={event_date}, teams={team_a} vs {team_b}, type={pick_type}")

                try:
                    insert_query = """INSERT INTO events (
                            event_id, event_date, league,
                            team_a, team_b, pick_type,
                            player_team, stat_type, player_name, stat_threshold)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)"""
                    insert_values = (
                        event_id, event_date, league, team_a, team_b, pick_type,
                        player_team, stat_type, player_name, stat_threshold,
                    )
                    print(f"🔄 Executing SQL: {insert_query}")
                    print(f"🔄 With values: {insert_values}")

                    cursor.execute(insert_query, insert_values)
                    rows_affected = cursor.rowcount
                    print(f"🔄 Rows affected by INSERT: {rows_affected}")

                    if rows_affected > 0:
                        inserted += 1
                        print(f"✅ Inserted event: {event_id}")
                    else:
                        print(f"⚠️ No rows inserted for event: {event_id}")

                except Exception as insert_error:
                    if "Duplicate entry" in str(insert_error):
                        print(f"⚠️ Event already exists: {event_id}")
                    else:
                        print(f"❌ Failed to insert event {event_id}: {insert_error}")
                        import traceback
                        traceback.print_exc()
                        raise  # Re-raise if it's not a duplicate key error

                # After inserting the event, insert expert predictions referencing this specific event_id
                if pick_origin:
                    for expert_pick in pick_origin:
                        expert_name = expert_pick.get("name")
                        if expert_name and expert_name.strip():
                            # Validate and convert data types to match database constraints
                            try:
                                # Convert prediction to integer (0 or 1)
                                prediction = int(data.get("prediction", 1))
                                if prediction not in [0, 1]:
                                    prediction = 1  # Default to 1 if invalid

                                # Convert confidence to float between 0.0 and 1.0
                                confidence_raw = expert_pick.get("confidence")
                                if confidence_raw is not None:
                                    confidence = float(confidence_raw)
                                    # If confidence is > 1, assume it's a percentage and convert
                                    if confidence > 1.0:
                                        confidence = confidence / 100.0
                                    # Ensure it's within valid range
                                    confidence = max(0.0, min(1.0, confidence))
                                else:
                                    confidence = None

                                # Convert stat_threshold to float if provided
                                stat_threshold_raw = expert_pick.get("stat_threshold")
                                stat_threshold = float(stat_threshold_raw) if stat_threshold_raw is not None else None

                                print(f"🔄 Inserting prediction: expert={expert_name}, prediction={prediction}, confidence={confidence}")

                                cursor.execute(
                                    """INSERT INTO expert_predictions (
                                        event_id, expert_name, prediction, confidence,
                                        stat_threshold, team, game_date, league
                                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)""",
                                    (
                                        event_id,
                                        expert_name,
                                        prediction,
                                        confidence,
                                        stat_threshold,
                                        expert_pick.get("team"),
                                        event_date,
                                        league,
                                    ),
                                )
                                expert_predictions_inserted += 1
                                print(f"✅ Inserted expert prediction: {expert_name} for {event_id}")
                            except Exception as pred_error:
                                print(f"❌ Failed to insert expert prediction for {expert_name}: {pred_error}")
                                # Try fallback with basic columns only and validated data
                                try:
                                    prediction = int(data.get("prediction", 1))
                                    if prediction not in [0, 1]:
                                        prediction = 1

                                    confidence_raw = expert_pick.get("confidence")
                                    if confidence_raw is not None:
                                        confidence = float(confidence_raw)
                                        if confidence > 1.0:
                                            confidence = confidence / 100.0
                                        confidence = max(0.0, min(1.0, confidence))
                                    else:
                                        confidence = None

                                    cursor.execute(
                                        """INSERT INTO expert_predictions (
                                            event_id, expert_name, prediction, confidence
                                        ) VALUES (%s, %s, %s, %s)""",
                                        (
                                            event_id,
                                            expert_name,
                                            prediction,
                                            confidence,
                                        ),
                                    )
                                    expert_predictions_inserted += 1
                                    print(f"✅ Inserted expert prediction (basic): {expert_name} for {event_id}")
                                except Exception as fallback_error:
                                    print(f"❌ Failed to insert expert prediction (fallback): {fallback_error}")
                        else:
                            print(f"⚠️ Skipping empty expert name for {event_id}")

            conn.commit()
            print(f"✅ Transaction committed: {inserted} events, {expert_predictions_inserted} expert predictions")
            
    except Exception as e:
        # The connection context manager should handle rollback on exception.
        print(f"❌ Database transaction failed: {e}")
        return jsonify({"success": False, "message": f"Database transaction failed: {str(e)}"}), 500

    return jsonify({
        "success": True,
        "message": f"Successfully inserted {inserted} events and {expert_predictions_inserted} expert predictions.",
        "event_id": event_id,
        "events_inserted": inserted,
        "expert_predictions_inserted": expert_predictions_inserted
    })

@app.route("/api/experts", methods=["GET"])
def get_all_experts():
    """Return all distinct expert names from expert_predictions table."""
    try:
        with get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT DISTINCT expert_name FROM expert_predictions
                WHERE expert_name IS NOT NULL AND expert_name != ''
                ORDER BY expert_name
            """)
            rows = cursor.fetchall()
            experts = [row[0] for row in rows]
            return jsonify({"success": True, "experts": experts})
    except Exception as e:
        print(f"❌ Error fetching all experts: {e}")
        return jsonify({"success": False, "message": str(e), "experts": []}), 500

@app.route("/api/experts_by_date", methods=["GET"])
def experts_by_date():
    """Return distinct expert names that have predictions for the given date."""
    target_date = request.args.get("date") or datetime.today().strftime("%Y-%m-%d")
    try:
        with get_connection() as conn:
            cursor = conn.cursor()

            # Use JOIN with events table to filter by event_date (most reliable approach)
            cursor.execute("""
                SELECT DISTINCT ep.expert_name
                FROM expert_predictions ep
                JOIN events e ON ep.event_id = e.event_id
                WHERE e.event_date = %s
                AND ep.expert_name IS NOT NULL
                AND ep.expert_name != ''
            """, (target_date,))
            rows = cursor.fetchall()
            experts = [row[0] for row in rows]
            return jsonify({"success": True, "date": target_date, "experts": experts})

    except Exception as e:
        print(f"❌ Error fetching experts for {target_date}: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({"success": False, "message": str(e), "experts": []}), 500

if __name__ == "__main__":
    app.run(host='0.0.0.0', port=5000)
